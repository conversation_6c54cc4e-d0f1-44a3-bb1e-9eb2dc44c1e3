# CRUSH.md - Next.js Commands & Style

## Commands
```bash
npm run dev      # Start dev server with turbopack
npm run build    # Build for production  
npm run start    # Start production server
npm run lint     # Run ESLint with Next.js config
npm test         # Add Jest: npm install -D jest @testing-library/react
npm run test:watch
```

## Style Guidelines
- **Types**: `type` over `interface`, strict TypeScript
- **Imports**: `@/*` for src/, `next/*` for Next.js features
- **Components**: Arrow functions, `Readonly<{ prop: type }>` props
- **Naming**: camelCase vars, PascalCase components, UPPER_CASE constants
- **Styling**: Tailwind utility classes, responsive prefixes
- **Error Handling**: try/catch for async, Server Actions for forms
- **Structure**: App Router only, `app/loading.tsx`, `app/error.tsx`

## Patterns
- Server Components by default
- `fetch()` with `{ cache: 'no-store' }` for dynamic data
- `next/image` with width/height, `next/font` optimization
- Co-locate components with routes or use `src/components/`

## Testing Setup
```bash
npm install -D jest @testing-library/react jest-environment-jsdom
# Add test script to package.json
```